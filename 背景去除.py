import os
import sys
import threading
import warnings
import gc

import cv2
import numpy as np
import torch.cuda
from PIL import Image
from PyQt5.QtCore import (QPoint, QPropertyAnimation, QRect, Qt, QThread,
                          pyqtSignal)
from PyQt5.QtGui import (QBrush, QColor, QCursor, QFont, QFontMetrics, QIcon,
                         QPainter, QPalette, QPen, QPixmap)
from PyQt5.QtWidgets import (QApplication, QButtonGroup, QCheckBox, QFileDialog,
                             QFrame, QHBoxLayout, QLabel, QMessageBox,
                             QProgressBar, QPushButton, QRadioButton,
                             QSizePolicy, QVBoxLayout, QWidget)
from torchvision import transforms
from transformers import AutoModelForImageSegmentation
from torch import device, no_grad

__author__ = "ZKZ"
__contact__= "fuck"

def get_watermark_info():
    return f"Author: {__author__}, Contact: {__contact__}"
warnings.filterwarnings("ignore", category=FutureWarning, module='timm')
class InitializationWorker(QThread):
    update_progress = pyqtSignal(int, str)
    initialization_finished = pyqtSignal(object, object)
    cuda_status_message = pyqtSignal(str)

    def __init__(self, model_path, device_ref):
        super().__init__()
        self.model_path = model_path
        # 传递一个可变对象 (列表), 以便在线程中修改主应用的 device 属性
        self.device_ref = device_ref

    def run(self):
        """线程启动后执行此方法"""
        try:
            # 1. 检查CUDA环境 (这是一个耗时操作)
            self.update_progress.emit(10, "正在检查CUDA环境...")
            message = ""
            if torch.cuda.is_available():
                cuda_version_str = torch.version.cuda
                # 检查主版本号是否大于等于12
                if cuda_version_str and float(cuda_version_str.split('.')[0]) >= 12:
                    self.device_ref[0] = torch.device('cuda')
                    message = f"CUDA {cuda_version_str} 兼容, 将使用GPU加速."
                else:
                    self.device_ref[0] = torch.device('cpu')
                    message = f"CUDA版本不兼容 (需要>=12.0), 将使用CPU."
            else:
                self.device_ref[0] = torch.device('cpu')
                message = "未检测到CUDA, 将使用CPU."

            self.cuda_status_message.emit(message)
            self.update_progress.emit(30, "CUDA检查完成.")
            torch.cuda.empty_cache() # 释放检查时可能占用的显存

            # 2. 加载模型 (这是最耗时的操作)
            self.update_progress.emit(40, "正在加载模型, 请耐心等待...")
            model = AutoModelForImageSegmentation.from_pretrained(
                self.model_path, trust_remote_code=True
            )
            model.to(self.device_ref[0])
            model.eval()
            self.update_progress.emit(85, "模型加载成功!")

            # 3. 创建图像转换器
            image_size = (1024, 1024)
            transform_image = transforms.Compose([
                transforms.Resize(image_size),
                transforms.ToTensor(),
                transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
            ])
            self.update_progress.emit(100, "初始化完成, 准备就绪!")

            # 4. 发送完成信号，并附带加载好的对象
            self.initialization_finished.emit(model, transform_image)

        except Exception as e:
            error_message = f"初始化失败: {e}"
            print(error_message)
            self.update_progress.emit(0, error_message)
            # 可以在这里发送一个失败信号
            self.cuda_status_message.emit("错误: 模型或环境加载失败，请检查文件或依赖。")

# 定义当前目录
if getattr(sys, 'frozen', False):
    current_dir = sys._MEIPASS
else:
    current_dir = os.path.dirname(os.path.abspath(__file__))

class TitleBar(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.initUI()
        self.start = QPoint(0, 0)
        self.pressing = False

    def initUI(self):
        layout = QHBoxLayout()
        layout.setContentsMargins(0, 0, 0, 2)
        layout.addStretch()
        min_btn = QPushButton()
        min_btn.setIcon(QIcon(os.path.join(current_dir, 'minimize.png')))
        min_btn.setFixedSize(30, 30)
        min_btn.clicked.connect(self.parent.showMinimized)
        min_btn.setStyleSheet("QPushButton { border: none; background: transparent; } QPushButton:hover { background-color: #404040; }")
        close_btn = QPushButton()
        close_btn.setIcon(QIcon(os.path.join(current_dir, 'close.png')))
        close_btn.setFixedSize(30, 30)
        close_btn.clicked.connect(self.parent.close)
        close_btn.setStyleSheet("QPushButton { border: none; background: transparent; } QPushButton:hover { background-color: #ff4444; }")
        layout.addWidget(min_btn)
        layout.addWidget(close_btn)
        self.setLayout(layout)
        self.setStyleSheet("QWidget { background-color: #303030; border-top-left-radius: 20px; border-top-right-radius: 20px; }")

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.start = self.mapToGlobal(event.pos())
            self.pressing = True

    def mouseMoveEvent(self, event):
        if self.pressing:
            self.end = self.mapToGlobal(event.pos())
            self.movement = self.end - self.start
            self.parent.setGeometry(self.parent.mapToGlobal(self.movement).x(),
                                  self.parent.mapToGlobal(self.movement).y(),
                                  self.parent.width(),
                                  self.parent.height())
            self.start = self.end

    def mouseReleaseEvent(self, event):
        self.pressing = False

class ImageComparisonWidget(QLabel):
    def __init__(self, parent=None, parent_app=None):
        super().__init__(parent)
        self.parent_app = parent_app
        self.original_image = None
        self.processed_image = None
        self.slider_position = 0.5
        self.dragging = False
        self.setMinimumSize(400, 300)
        self.setCursor(Qt.SplitHCursor)
        self.setStyleSheet("QLabel { background-color: #393939; border: 2px dashed #666; border-radius: 10px; }")
        
    def set_images(self, original_path, processed_path=None):
        self.original_image = QPixmap(original_path)
        self.processed_image = QPixmap(processed_path if processed_path else original_path)
        self.update()

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.dragging = True
            self.update_slider_position(event.pos())

    def mouseMoveEvent(self, event):
        if self.dragging:
            self.update_slider_position(event.pos())

    def mouseReleaseEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.dragging = False

    def update_slider_position(self, pos):
        if self.original_image:
            widget_width = self.width()
            x = max(0, min(pos.x(), widget_width))
            self.slider_position = x / widget_width
            self.update()
        
    def paintEvent(self, event):
        if self.original_image and self.processed_image:
            painter = QPainter(self)
            scaled_size = self.size()
            margin = 20
            scaled_size.setWidth(scaled_size.width() - margin)
            scaled_size.setHeight(scaled_size.height() - margin)
            scaled_original = self.original_image.scaled(scaled_size, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            scaled_processed = self.processed_image.scaled(scaled_size, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            x = (self.width() - scaled_original.width()) // 2
            y = (self.height() - scaled_original.height()) // 2
            painter.drawPixmap(x, y, scaled_processed)
            width = int(scaled_original.width() * self.slider_position)
            painter.drawPixmap(x, y, scaled_original, 0, 0, width, scaled_original.height())
            pen = QPen(Qt.white, 2)
            painter.setPen(pen)
            split_x = x + width
            dot_radius = 4
            line_height = scaled_original.height()
            painter.drawLine(split_x, y, split_x, y + line_height)
            painter.setBrush(Qt.white)
            painter.drawEllipse(QPoint(split_x, y), dot_radius, dot_radius)
            painter.drawEllipse(QPoint(split_x, y + line_height), dot_radius, dot_radius)
        else:
            painter = QPainter(self)
            painter.setPen(QPen(QColor("#666666")))
            painter.drawText(self.rect(), Qt.AlignCenter, "拖放图片到这里\n或点击左侧上传按钮")

class WorkArea(QFrame):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_app = parent
        self.initUI()
        
    def initUI(self):
        self.layout = QVBoxLayout(self)
        self.setAcceptDrops(True)
        self.setStyleSheet("WorkArea { background-color: #2a2a2a; border-radius: 10px; margin: 0 15px 0px 10px; padding: 20px; }")
        self.comparison_widget = ImageComparisonWidget(parent_app=self.parent_app)
        self.layout.addWidget(self.comparison_widget)
        
    def dragEnterEvent(self, event):
        if event.mimeData().hasUrls() and not self.parent_app.is_batch_mode:
            event.acceptProposedAction()
            
    def dropEvent(self, event):
        if not self.parent_app.is_batch_mode:
            files = [url.toLocalFile() for url in event.mimeData().urls()]
            if files:
                self.parent_app.process_dropped_image(files[0])
                
    def clear_preview(self):
        self.comparison_widget.original_image = None
        self.comparison_widget.processed_image = None
        self.comparison_widget.update()

class ModernButton(QPushButton):
    def __init__(self, text, parent=None):
        super().__init__(text, parent)
        self.setStyleSheet("""
            QPushButton { background-color: #007acc; color: white; border: none; border-radius: 10px; padding: 10px 20px; font-size: 16px; font-weight: 500; }
            QPushButton:hover { background-color: #005a9e; }
            QPushButton:pressed { background-color: #004a8e; }
            QPushButton:disabled { background-color: #555; color: #888; }
        """)

class ModernRadioButton(QRadioButton):
    def __init__(self, text, parent=None):
        super().__init__(text, parent)
        self.setStyleSheet("""
            QRadioButton { color: #b0b0b0; font-size: 16px; font-weight: 500; font-family: 'Microsoft YaHei', Arial, sans-serif; spacing: 16px; background: transparent; padding: 5px 0; }
            QRadioButton::indicator { width: 22px; height: 22px; border-radius: 11px; border: 2px solid #666; background: transparent; margin-top: 2px; }
            QRadioButton::indicator:checked { background-color: #814aef; border-color: #666; }
            QRadioButton::indicator:unchecked { background-color: transparent; border-color: #666; }
            QRadioButton:checked { color: white; }
        """)

class PathLabel(QLabel):
    def __init__(self, text='', parent=None):
        super().__init__(text, parent)
        self.full_text = text
        self.setTextInteractionFlags(Qt.TextSelectableByMouse)
        self.setWordWrap(False)
        self.setFixedWidth(350)
        self.setContentsMargins(0, 0, 5, 0)
        self.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        
    def setText(self, text):
        self.full_text = text
        metrics = QFontMetrics(self.font())
        available_width = self.width() - 30
        if ':' in text:
            drive, path = text.split(':', 1)
            drive = drive + ':'
            parts = path.strip('/').split('/')
            if len(parts) > 3:
                displayed_path = f"{drive}/{parts[0]}/{parts[1]}/.../{parts[-1]}"
            else:
                displayed_path = text
            if metrics.horizontalAdvance(displayed_path) > available_width:
                displayed_path = metrics.elidedText(displayed_path, Qt.ElideMiddle, available_width)
        else:
            displayed_path = metrics.elidedText(text, Qt.ElideMiddle, available_width)
        super().setText(displayed_path)
        self.setToolTip(text)
    
    def resizeEvent(self, event):
        super().resizeEvent(event)
        self.setText(self.full_text)

class ToggleSwitch(QWidget):
    toggled = pyqtSignal(bool)
    def __init__(self):
        super().__init__()
        self.setFixedSize(50, 26)
        self.is_checked = False
        self.bg_color = QPushButton(self)
        self.bg_color.setGeometry(0, 0, 50, 26)
        self.bg_color.setStyleSheet("QPushButton { background-color: #b0b0b0; border-radius: 13px; border: 2px solid #666; }")
        self.bg_color.setFlat(True)
        self.bg_color.clicked.connect(self.toggle)
        self.knob = QPushButton(self)
        self.knob.setGeometry(2, 2, 21, 21)
        self.knob.setStyleSheet("QPushButton { background-color: #ffffff; border-radius: 10px; }")
        self.knob.setFlat(True)
        self.knob.clicked.connect(self.toggle)

    def toggle(self):
        if self.is_checked:
            self.is_checked = False
            self.animate(27, 2)
            self.bg_color.setStyleSheet("QPushButton { background-color: #b0b0b0; border-radius: 13px; border: 2px solid #666; }")
        else:
            self.is_checked = True
            self.animate(2, 27)
            self.bg_color.setStyleSheet("QPushButton { background-color: #814aef; border-radius: 13px; border: 2px solid #666; }")
        self.toggled.emit(self.is_checked)

    def animate(self, start, end):
        self.animation = QPropertyAnimation(self.knob, b"geometry")
        self.animation.setDuration(200)
        self.animation.setStartValue(QRect(start, 2, 21, 21))
        self.animation.setEndValue(QRect(end, 2, 21, 21))
        self.animation.start()

class BackgroundRemoverApp(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowFlags(Qt.FramelessWindowHint)
        self.current_image = None
        self.processed_image_removed = None
        self.processed_image_black = None
        self.is_batch_mode = False
        self.output_folder = None
        self.model = None
        self.transform_image = None
        # 使用列表包装device，使其成为可在线程间共享和修改的可变对象
        self.device = [torch.device('cpu')] 

        self.initUI()
        self.start_initialization()

        self.center_and_raise()
        self.setMinimumSize(900, 800)

    def start_initialization(self):
        """配置并启动后台工作线程来加载模型"""
        # 在模型加载完成前，禁用处理按钮
        self.run_btn.setEnabled(False)
        self.progress_label.setText("正在初始化, 请稍候...")
        
        model_path = os.path.join(current_dir, 'briaai/RMBG-2.0')
        
        # 创建工作线程实例
        self.init_worker = InitializationWorker(model_path, self.device)
        
        # 连接信号到槽函数
        self.init_worker.update_progress.connect(self.update_status)
        self.init_worker.initialization_finished.connect(self.on_initialization_complete)
        self.init_worker.cuda_status_message.connect(self.log_system_status)

        # 启动线程
        self.init_worker.start()

    def update_status(self, value, text):
        """槽函数：更新进度条和状态标签"""
        self.progress_bar.setValue(value)
        self.progress_label.setText(text)

    def on_initialization_complete(self, model, transform):
        """槽函数：当初始化完成时调用"""
        self.model = model
        self.transform_image = transform
        # 初始化完成后，启用处理按钮
        self.run_btn.setEnabled(True)
        print("后台初始化完成.")

    def log_system_status(self, message):
        """槽函数：打印系统状态信息 (如CUDA状态)"""
        print(f"系统状态: {message}")
        # 如果需要，也可以将此信息显示在UI的某个标签上

    def center_and_raise(self):
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        x = (screen.width() - size.width()) // 2
        y = (screen.height() - size.height()) // 2
        self.move(x, y)
        self.raise_()
        self.activateWindow()

    def initUI(self):
        self.setWindowTitle('背景去除')
        self.setWindowIcon(QIcon(os.path.join(current_dir, 'bjqc.ico')))
        self.setGeometry(100, 100, 1200, 800)
        self.setStyleSheet("""
            QWidget { background-color: #1a1a1a; border-radius: 10px; color: #b0b0b0; font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 16px; font-weight: 500; }
            QFrame, QLabel, QVBoxLayout { border-radius: 20px; }
            QLabel { background: transparent; margin: 0; padding: 0; }
        """)
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        self.title_bar = TitleBar(self)
        main_layout.addWidget(self.title_bar)
        
        content_layout = QHBoxLayout()
        content_layout.setContentsMargins(0, 0, 0, 0)

        sidebar = QFrame()
        sidebar.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        sidebar.setMaximumWidth(385)
        sidebar.setStyleSheet("QFrame { background-color: #303030; border-radius: 10px; margin: 0 10px 0px 15px; padding: 10px; }")
        sidebar_layout = QVBoxLayout(sidebar)

        icon_title_container = QWidget()
        icon_title_layout = QHBoxLayout(icon_title_container)
        icon_title_layout.setContentsMargins(5, 10, 5, 10)
        icon_title_layout.setSpacing(10)
        icon_label = QLabel()
        icon_pixmap = QPixmap(os.path.join(current_dir, 'bjqc.png'))
        icon_label.setPixmap(icon_pixmap.scaled(60, 60, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        icon_title_layout.addWidget(icon_label)
        title_label = QLabel("背景去除")
        title_label.setStyleSheet("font-size: 30px; font-weight: 600; color: #b0b0b0; font-family: 'Microsoft YaHei', Arial, sans-serif;")
        icon_title_layout.addWidget(title_label)
        icon_title_container.setStyleSheet("QWidget { margin: 0; padding: 2; background-color: transparent; }")
        icon_title_layout.setStretch(0, 0)
        icon_title_layout.setStretch(1, 1)
        icon_title_container.setSizePolicy(QSizePolicy.Minimum, QSizePolicy.Fixed)
        sidebar_layout.addWidget(icon_title_container)

        switch_container = QWidget()
        switch_layout = QHBoxLayout(switch_container)
        switch_layout.setContentsMargins(5, 10, 5, 15)
        switch_layout.setSpacing(15)
        mode_switch_label = QLabel("批量处理模式")
        mode_switch_label.setStyleSheet("font-size: 20px; font-weight: 500; padding-bottom: 2px; color: #b0b0b0; font-family: 'Microsoft YaHei', Arial, sans-serif;")
        self.mode_switch = ToggleSwitch()
        self.mode_switch.toggled.connect(self.toggle_mode)
        switch_layout.addWidget(mode_switch_label)
        switch_layout.addWidget(self.mode_switch)
        switch_layout.addStretch(1)
        switch_container.setStyleSheet("QWidget { margin: 0; padding: 0; background-color: transparent; }")
        switch_layout.setStretch(0, 0)
        switch_layout.setStretch(1, 0)
        switch_container.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        sidebar_layout.addWidget(switch_container)

        self.upload_btn = ModernButton('文件上传')
        self.upload_btn.clicked.connect(self.select_single_image)
        sidebar_layout.addWidget(self.upload_btn)

        self.single_output_btn = ModernButton('选择输出文件夹')
        self.single_output_btn.clicked.connect(self.select_output_folder)
        self.single_output_label = PathLabel('输出文件夹: 未选择')
        sidebar_layout.addWidget(self.single_output_btn)
        sidebar_layout.addWidget(self.single_output_label)

        self.batch_widgets = QWidget()
        batch_layout = QVBoxLayout(self.batch_widgets)
        batch_layout.setContentsMargins(0, 0, 0, 0)
        batch_layout.setSpacing(0)
        self.batch_widgets.setStyleSheet("QWidget { background: transparent; }")
        self.input_folder_btn = ModernButton('选择输入文件夹')
        self.input_folder_btn.clicked.connect(self.select_input_folder)
        self.input_folder_label = PathLabel('输入文件夹: 未选择')
        self.output_folder_btn = ModernButton('选择输出文件夹')
        self.output_folder_btn.clicked.connect(self.select_output_folder)
        self.output_folder_label = PathLabel('输出文件夹: 未选择')
        batch_layout.addWidget(self.input_folder_btn)
        batch_layout.addWidget(self.input_folder_label)
        batch_layout.addWidget(self.output_folder_btn)
        batch_layout.addWidget(self.output_folder_label)
        sidebar_layout.addWidget(self.batch_widgets)
        self.batch_widgets.hide()

        options_label = QLabel("处理选项")
        options_label.setStyleSheet("font-size: 17px; font-weight: 500; color: #b0b0b0; margin-top: 20px; font-family: 'Microsoft YaHei', Arial, sans-serif;")
        sidebar_layout.addWidget(options_label)

        self.mode_group = QButtonGroup(self)
        self.remove_bg_radio = ModernRadioButton('去除背景')
        self.fill_black_radio = ModernRadioButton('获取图像轮廓')
        self.both_radio = ModernRadioButton('去除背景并获取轮廓')
        self.both_radio.setChecked(True)
        self.mode_group.addButton(self.remove_bg_radio)
        self.mode_group.addButton(self.fill_black_radio)
        self.mode_group.addButton(self.both_radio)
        sidebar_layout.addWidget(self.remove_bg_radio)
        sidebar_layout.addWidget(self.fill_black_radio)
        sidebar_layout.addWidget(self.both_radio)

        sidebar_layout.addStretch()

        self.progress_label = QLabel('准备就绪')
        self.progress_label.setStyleSheet("font-size: 16px; font-weight: 500; color: #b0b0b0; font-family: 'Microsoft YaHei', Arial, sans-serif; margin-bottom: 8px;")
        sidebar_layout.addWidget(self.progress_label)

        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet("""
            QProgressBar { border: 2px solid #666; border-radius: 10px; text-align: center; background-color: #333; height: 25px; }
            QProgressBar::chunk { background-color: #814aef; border-radius: 10px; }
        """)
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(True)
        sidebar_layout.addWidget(self.progress_bar)

        self.run_btn = ModernButton('开始处理')
        self.run_btn.clicked.connect(self.start_processing)
        sidebar_layout.addWidget(self.run_btn)

        self.work_area = WorkArea(self)
        
        content_layout.addWidget(sidebar, 1)
        content_layout.addWidget(self.work_area, 2)
        main_layout.addLayout(content_layout)
        self.setLayout(main_layout)

        self.watermark_label = QLabel("ZKZ20241202v1.0")
        self.watermark_label.setStyleSheet("QLabel { color: rgba(176, 176, 176, 100); font-size: 12px; font-family: 'Microsoft YaHei', Arial, sans-serif; margin-right: 15px; margin-bottom: 2px; }")
        self.watermark_label.setAlignment(Qt.AlignRight | Qt.AlignBottom)
        main_layout.addWidget(self.watermark_label, alignment=Qt.AlignRight | Qt.AlignBottom)

    def toggle_mode(self, state):
        self.is_batch_mode = state
        if state:
            print("批量处理模式已打开")
            self.batch_widgets.show()
            self.upload_btn.hide()
            self.single_output_btn.hide()
            self.single_output_label.hide()
            self.work_area.clear_preview()
            self.input_folder = None
            self.output_folder = None
            self.input_folder_label.setText('输入文件夹: 未选择')
            self.output_folder_label.setText('输出文件夹: 未选择')
        else:
            print("批量处理模式已关闭")
            self.batch_widgets.hide()
            self.upload_btn.show()
            self.single_output_btn.show()
            self.single_output_label.show()
            self.output_folder = None
            self.single_output_label.setText('输出文件夹: 未选择')
            self.processed_image_removed = None

    def select_single_image(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "选择图像", "", "Image Files (*.png *.jpg *.jpeg *.bmp *.tiff)")
        if file_path:
            self.current_image = file_path # 确保 current_image 被设置
            self.processed_image_removed = None
            self.processed_image_black = None
            self.work_area.comparison_widget.set_images(file_path)
            self.progress_label.setText('图像已上传, 等待处理')
            # 自动将输出文件夹设置为图像所在目录
            self.uploaded_image_folder = os.path.dirname(file_path)
            self.output_folder = self.uploaded_image_folder
            self.single_output_label.setText(f'输出文件夹: {self.output_folder}')


    def process_dropped_image(self, image_path):
        self.current_image = image_path
        self.work_area.comparison_widget.set_images(image_path)
        self.uploaded_image_folder = os.path.dirname(image_path)
        # 拖入图片后，同样自动设置输出文件夹
        self.output_folder = self.uploaded_image_folder
        self.single_output_label.setText(f'输出文件夹: {self.output_folder}')

    def select_input_folder(self):
        folder = QFileDialog.getExistingDirectory(self, '选择输入文件夹')
        if folder:
            self.input_folder = folder
            self.input_folder_label.setText(f'输入文件夹: {folder}')

    def select_output_folder(self):
        folder = QFileDialog.getExistingDirectory(self, "选择输出文件夹")
        if folder:
            self.output_folder = folder
            if self.is_batch_mode:
                self.output_folder_label.setText(f'输出文件夹: {self.output_folder}')
            else:
                self.single_output_label.setText(f'输出文件夹: {self.output_folder}')

    def start_processing(self):
        # 检查模型是否已加载
        if self.model is None or self.transform_image is None:
            QMessageBox.warning(self, "警告", "模型正在加载中, 请稍候...")
            return

        if self.is_batch_mode:
            if not hasattr(self, 'input_folder') or not self.input_folder or not self.output_folder:
                QMessageBox.warning(self, "警告", "请先选择输入和输出文件夹")
                return
            # 使用线程处理批量任务，避免UI冻结
            batch_thread = threading.Thread(target=self.run_batch_processing)
            batch_thread.start()
        else:
            if not self.current_image:
                QMessageBox.warning(self, "警告", "请先选择一张图片")
                return
            if not self.output_folder:
                QMessageBox.warning(self, "警告", "请选择输出文件夹")
                return
            # 使用线程处理单图任务
            single_thread = threading.Thread(target=self.process_single_image, args=(self.current_image,))
            single_thread.start()

    def generate_unique_path(self, path):
        if not os.path.exists(path):
            return path
        base, ext = os.path.splitext(path)
        counter = 1
        while os.path.exists(path):
            path = f"{base}_{counter}{ext}"
            counter += 1
        return path

    def process_single_image(self, image_path):
        try:
            # 更新UI必须在主线程，但这里是子线程，所以我们通过信号来更新
            # self.update_status(0, '正在处理图像...') -> 这应该用信号实现，但为简化暂时只在控制台打印
            print("开始处理单张图片...")
            
            output_dir = self.output_folder
            base_name = os.path.basename(image_path)
            name, _ = os.path.splitext(base_name)
            
            self.processed_image_removed = None
            self.processed_image_black = None

            # 决定文件名（不带计数器，直接用原名）
            removed_bg_path = os.path.join(output_dir, f"{name}_removed.png")
            outline_path = os.path.join(output_dir, f"{name}_outline.png")
            
            # 保证路径唯一
            removed_bg_path = self.generate_unique_path(removed_bg_path)
            outline_path = self.generate_unique_path(outline_path)

            if self.remove_bg_radio.isChecked():
                self.remove_background(image_path, removed_bg_path)
                self.processed_image_removed = removed_bg_path
                self.work_area.comparison_widget.set_images(image_path, self.processed_image_removed)

            elif self.fill_black_radio.isChecked():
                self.fill_black(image_path, outline_path)
                self.processed_image_black = outline_path
                self.work_area.comparison_widget.set_images(image_path, self.processed_image_black)

            elif self.both_radio.isChecked():
                self.remove_background(image_path, removed_bg_path)
                self.processed_image_removed = removed_bg_path
                self.fill_black(self.processed_image_removed, outline_path)
                self.processed_image_black = outline_path
                self.work_area.comparison_widget.set_images(image_path, self.processed_image_removed)

            print("处理完成!")
            # 可以在这里发送信号通知UI处理完成
            
        except Exception as e:
            print(f"处理图像时出错: {str(e)}")
            # 发送错误信号

    def run_batch_processing(self):
        image_files = [f for f in os.listdir(self.input_folder)
                       if f.lower().endswith(('png', 'jpg', 'jpeg', 'bmp', 'tiff', 'webp'))]

        if not image_files:
            # 需要在主线程显示QMessageBox
            print("警告: 输入文件夹中没有找到图像文件")
            return

        self.progress_bar.setMaximum(len(image_files))
        for i, filename in enumerate(image_files):
            try:
                self.update_status(i, f'正在处理: {filename}')
                image_path = os.path.join(self.input_folder, filename)
                name, _ = os.path.splitext(filename)
                
                removed_bg_path = os.path.join(self.output_folder, f"{name}_removed.png")
                outline_path = os.path.join(self.output_folder, f"{name}_outline.png")

                if self.remove_bg_radio.isChecked() or self.both_radio.isChecked():
                    self.remove_background(image_path, removed_bg_path)

                if self.fill_black_radio.isChecked() or self.both_radio.isChecked():
                    input_for_outline = removed_bg_path if self.both_radio.isChecked() else image_path
                    self.fill_black(input_for_outline, outline_path)

                self.progress_bar.setValue(i + 1)

            except Exception as e:
                print(f"处理图像 {filename} 时出错: {str(e)}")
                continue
        
        self.update_status(len(image_files), '所有图像处理完成')
        # 在主线程中显示完成信息
        QMetaObject.invokeMethod(QMessageBox, 'information', Qt.QueuedConnection,
                                 Q_ARG(QWidget, self), Q_ARG(str, "成功"), Q_ARG(str, "所有图像处理完成"))

    def remove_background(self, image_path, output_path):
        image = Image.open(image_path).convert("RGBA")
        image_rgb = image.convert("RGB")
        # 使用 self.device[0] 来获取正确的设备
        image_tensor = self.transform_image(image_rgb).unsqueeze(0).to(self.device[0])

        with no_grad():
            preds = self.model(image_tensor)[-1].sigmoid().cpu()

        pred = preds[0].squeeze()
        pred_pil = transforms.ToPILImage()(pred)
        mask = pred_pil.resize(image.size)
        
        image.putalpha(mask)
        image.save(output_path, "PNG")

    def fill_black(self, image_path, output_path):
        image = Image.open(image_path).convert('RGBA')
        image_array = np.array(image)
        alpha_channel = image_array[:, :, 3]
        _, binary_alpha = cv2.threshold(alpha_channel, 1, 255, cv2.THRESH_BINARY)
        bordered_alpha = cv2.copyMakeBorder(binary_alpha, 1, 1, 1, 1, cv2.BORDER_CONSTANT, value=0)
        edges = cv2.Canny(bordered_alpha, 100, 200)
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        result_image = np.zeros((bordered_alpha.shape[0], bordered_alpha.shape[1], 4), dtype=np.uint8)
        cv2.drawContours(result_image, contours, -1, (0, 0, 0, 255), 1)
        result_image = result_image[1:-1, 1:-1]
        result_image_pil = Image.fromarray(result_image)
        result_image_pil.save(output_path, "PNG")

if __name__ == "__main__":
    print("程序启动时包含隐藏水印信息:")
    print(get_watermark_info())

    app = QApplication(sys.argv)
    ex = BackgroundRemoverApp()
    ex.show()
    sys.exit(app.exec_())